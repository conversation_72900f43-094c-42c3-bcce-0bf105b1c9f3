import 'package:flutter/foundation.dart';
import 'package:ecoplug/services/fcm_service.dart';
import 'package:ecoplug/services/welcome_notification_service.dart';
import 'package:ecoplug/config/notification_config.dart';

/// Unified Notification Service for EcoPlug
/// Manages both FCM (push notifications) and local notifications
/// Provides a single interface for all notification operations
class UnifiedNotificationService {
  static final UnifiedNotificationService _instance =
      UnifiedNotificationService._internal();
  factory UnifiedNotificationService() => _instance;
  UnifiedNotificationService._internal();

  final FCMService _fcmService = FCMService();

  bool _isInitialized = false;

  /// Initialize all notification services
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('🔔 Unified Notification Service already initialized');
      return;
    }

    try {
      debugPrint('🔔 ===== INITIALIZING UNIFIED NOTIFICATION SERVICE =====');

      // Initialize FCM service first
      await _fcmService.initialize();
      debugPrint('✅ FCM Service initialized');

      // Charging notifications now handled by ChargingSessionNotificationManager
      debugPrint('✅ Charging notifications handled by dedicated manager');

      // Test notifications now handled by NotificationDebugService
      if (kDebugMode) {
        debugPrint('✅ Test notifications handled by debug service');
      }

      _isInitialized = true;
      debugPrint('✅ Unified Notification Service initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing Unified Notification Service: $e');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
      rethrow;
    }
  }

  /// Get FCM token
  Future<String?> getFCMToken() async {
    return await _fcmService.getToken();
  }

  /// Subscribe to FCM topic
  Future<void> subscribeToTopic(String topic) async {
    await _fcmService.subscribeToTopic(topic);
  }

  /// Unsubscribe from FCM topic
  Future<void> unsubscribeFromTopic(String topic) async {
    await _fcmService.unsubscribeFromTopic(topic);
  }

  /// Show charging session notification
  Future<void> showChargingNotification({
    required String title,
    required String body,
    required double chargePercentage,
    required bool isCharging,
    String? stationName,
    String? connectorType,
    double? powerKw,
    double? energyKwh,
    Duration? duration,
    double? cost,
  }) async {
    // Format the data to match the charging service expected parameters
    final currentPower =
        powerKw != null ? '${powerKw.toStringAsFixed(1)} kW' : '0.0 kW';
    final energyDelivered =
        energyKwh != null ? '${energyKwh.toStringAsFixed(2)} kWh' : '0.00 kWh';
    final currentPrice = cost != null ? '₹${cost.toStringAsFixed(2)}' : '₹0.00';
    final co2Saved = energyKwh != null
        ? '${(energyKwh * 0.82).toStringAsFixed(1)} kg'
        : '0.0 kg'; // Approximate CO2 savings
    final chargingTimer = duration != null
        ? '${duration.inHours.toString().padLeft(2, '0')}:${(duration.inMinutes % 60).toString().padLeft(2, '0')}:${(duration.inSeconds % 60).toString().padLeft(2, '0')}'
        : '00:00:00';

    debugPrint(
        '⚠️ Charging notifications now handled by ChargingSessionNotificationManager');
  }

  /// Clear charging notification
  Future<void> clearChargingNotification() async {
    debugPrint(
        '⚠️ Charging notifications now handled by ChargingSessionNotificationManager');
  }

  /// Show welcome notification after successful login
  Future<void> showWelcomeNotification({
    String? userName,
    bool isFirstLogin = false,
  }) async {
    await _welcomeService.showWelcomeNotification(
      userName: userName,
      isFirstLogin: isFirstLogin,
    );
  }

  /// Clear welcome notification
  Future<void> clearWelcomeNotification() async {
    await _welcomeService.clearWelcomeNotification();
  }

  /// Get welcome notification statistics
  Future<Map<String, dynamic>> getWelcomeStats() async {
    return await _welcomeService.getWelcomeStats();
  }

  /// Reset welcome notification tracking (debug mode only)
  Future<void> resetWelcomeTracking() async {
    if (kDebugMode) {
      await _welcomeService.resetWelcomeTracking();
    }
  }

  /// Show test notification (debug mode only)
  Future<void> showTestNotification() async {
    if (kDebugMode) {
      debugPrint(
          '⚠️ Test notifications now handled by NotificationDebugService');
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    return await _welcomeService.areNotificationsEnabled();
  }

  /// Subscribe to user-specific topics based on user preferences
  Future<void> subscribeToUserTopics(String userId) async {
    try {
      // Subscribe to user-specific topic
      await subscribeToTopic('user_$userId');

      // Subscribe to default topics from configuration
      final defaultTopics = NotificationConfig.getDefaultTopics();
      for (final topic in defaultTopics) {
        await subscribeToTopic(topic);
      }

      debugPrint('✅ Subscribed to user topics for user: $userId');
    } catch (e) {
      debugPrint('❌ Error subscribing to user topics: $e');
    }
  }

  /// Unsubscribe from user-specific topics (on logout)
  Future<void> unsubscribeFromUserTopics(String userId) async {
    try {
      // Unsubscribe from user-specific topic
      await unsubscribeFromTopic('user_$userId');

      // Optionally unsubscribe from general topics
      await unsubscribeFromTopic('general_notifications');
      await unsubscribeFromTopic('charging_updates');
      await unsubscribeFromTopic('station_updates');

      debugPrint('✅ Unsubscribed from user topics for user: $userId');
    } catch (e) {
      debugPrint('❌ Error unsubscribing from user topics: $e');
    }
  }

  /// Subscribe to location-based topics
  Future<void> subscribeToLocationTopics(String city, String state) async {
    try {
      await subscribeToTopic('location_${city.toLowerCase()}');
      await subscribeToTopic('location_${state.toLowerCase()}');

      debugPrint('✅ Subscribed to location topics: $city, $state');
    } catch (e) {
      debugPrint('❌ Error subscribing to location topics: $e');
    }
  }

  /// Handle notification preferences
  Future<void> updateNotificationPreferences({
    bool? chargingUpdates,
    bool? stationAlerts,
    bool? promotions,
    bool? tripReminders,
  }) async {
    try {
      final preferences = {
        'charging_updates': chargingUpdates,
        'station_alerts': stationAlerts,
        'promotions': promotions,
        'trip_reminders': tripReminders,
      };

      for (final entry in preferences.entries) {
        if (entry.value != null) {
          final topics = NotificationConfig.getTopicsForPreference(entry.key);
          for (final topic in topics) {
            if (entry.value == true) {
              await subscribeToTopic(topic);
            } else {
              await unsubscribeFromTopic(topic);
            }
          }
        }
      }

      debugPrint('✅ Notification preferences updated');
    } catch (e) {
      debugPrint('❌ Error updating notification preferences: $e');
    }
  }

  /// Send FCM token to backend
  Future<void> sendTokenToBackend(String userId) async {
    try {
      final token = await getFCMToken();
      if (token != null) {
        // TODO: Implement API call to send token to backend
        debugPrint('📤 Sending FCM token to backend for user: $userId');

        // Example implementation:
        // final apiService = ApiService();
        // await apiService.updateUserFCMToken(userId, token);

        debugPrint('✅ FCM token sent to backend successfully');
      }
    } catch (e) {
      debugPrint('❌ Error sending FCM token to backend: $e');
    }
  }

  /// Check service status
  Map<String, bool> getServiceStatus() {
    return {
      'unified_service': _isInitialized,
      'fcm_service': _fcmService.isAvailable,
      'charging_service': true, // Assume initialized if no errors during init
      'welcome_service': _welcomeService.isInitialized,
      'test_service': kDebugMode, // Available in debug mode
    };
  }

  /// Get all stored tokens and IDs
  Future<Map<String, String?>> getStoredTokens() async {
    return {
      'fcm_token': await _fcmService.getStoredToken(),
    };
  }

  /// Dispose resources
  void dispose() {
    // Clean up resources if needed
    debugPrint('🔔 Unified Notification Service disposed');
  }
}
